"use client";

import React, { use<PERSON>emo, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { User, UpdatePassword } from "@/constants/user";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/input/Select";
import { Calendar } from "@/components/ui/calendar";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { cn } from "@/lib/utils";
import axios from "axios";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { format } from "date-fns";
import { CalendarIcon, ChevronDown, Loader2 } from "lucide-react";
import { agentPostMethod } from "@/utils/api";
import { debounce } from "lodash";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import OtpVerificationScreen from "../otp-verfication/OtpVerificationScreen";
import { useCustomSession } from "@/hooks/use-custom-session";

const passwordFormSchema = z
  .object({
    oldPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    newPassword: z
      .string()
      .min(8, {
        message: "Password must be at least 8 characters.",
      })
      .refine((password) => /[A-Z]/.test(password), {
        message: "Password must contain at least one uppercase letter.",
      }),
    confirmPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Passwords do not match",
  });

// More comprehensive phone regex that validates international formats
const phoneRegex = new RegExp(
  /^\+?([0-9]{1,3})?[-.\s]?\(?([0-9]{1,4})\)?[-.\s]?([0-9]{1,4})[-.\s]?([0-9]{1,9})$/
);
const personalDetailsSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  gender: z.string().min(1, "Gender is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  nationality: z.string().min(1, "Nationality is required"),
  title: z.string().min(1, "Title is required"),
  alternatePhone: z
    .string()
    .optional()
    .refine((val) => !val || phoneRegex.test(val), {
      message: "Invalid Number!",
    }),
  email: z.string().email({ message: "Email is required" }),
  phone: z.preprocess(
    (val) => (val === null || val === undefined ? "" : val),
    z
      .string()
      .min(1, { message: "Phone number is required" })
      .refine((val) => phoneRegex.test(val), {
        message: "Invalid Number!",
      })
  ),
});

// Email regex for more comprehensive validation
const emailRegex = new RegExp(
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
);

type PasswordFormValues = z.infer<typeof passwordFormSchema>;
type PersonalFormValues = z.infer<typeof personalDetailsSchema>;

export interface ProfileDetailsProps {
  user: User | null;
  updateUser: (data: User) => Promise<void>;
  updatedStatus: string | null;
}

interface CountryState {
  search: string;
  loading: boolean;
  countryList: Array<{ code: string; name: string }>;
}

const ProfileDetails: React.FC<ProfileDetailsProps> = ({
  user,
  updateUser,
  updatedStatus,
}) => {
  const { data: session } = useCustomSession();
  const token = session?.accessToken;

  // Combined editing state for Personal Information & Contact Information
  const [editingPersonalAndContact, setEditingPersonalAndContact] =
    useState<boolean>(false);

  // Separate editing state for Security Information
  const [editingSecurity, setEditingSecurity] = useState<boolean>(false);

  // OTP states
  const [showEmailOtp, setShowEmailOtp] = useState<boolean>(false);
  const [showPhoneOtp, setShowPhoneOtp] = useState<boolean>(false);

  // Loading states
  const [loadingEmailOtp, setLoadingEmailOtp] = useState<boolean>(false);
  const [loadingEmailOtpVerify, setLoadingEmailOtpVerify] =
    useState<boolean>(false);
  const [loadingPhoneOtp, setLoadingPhoneOtp] = useState<boolean>(false);
  const [loadingPhoneOtpVerify, setLoadingPhoneOtpVerify] =
    useState<boolean>(false);

  // Error states
  const [otpErrorMessage, setOtpErrorMessage] = useState<string | null>(null);
  const [phoneOtpErrorMessage, setPhoneOtpErrorMessage] = useState<
    string | null
  >(null);
  const [passwordStatus, setPasswordStatus] = useState<{
    status: "success" | "error" | null;
    message: string | null;
  }>({
    status: null,
    message: null,
  });

  // Country selection state
  const [country, setCountry] = useState<CountryState>({
    search: "",
    loading: false,
    countryList: [],
  });
  const [open, setOpen] = useState(false);

  // State to control the popover for date of birth
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Form initialization
  const defaultValues: Partial<PersonalFormValues> = user ?? {};

  const personalForm = useForm<PersonalFormValues>({
    resolver: zodResolver(personalDetailsSchema),
    defaultValues,
    mode: "onChange",
  });

  const passForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    mode: "onChange",
  });

  // Debounced country search
  const debouncedHandleCountry = useMemo(
    () =>
      debounce(async (query: string) => {
        if (query !== "") {
          try {
            const response = await agentPostMethod(
              "flight/suggest-country",
              { query },
              token ?? ""
            );
            setCountry({
              search: query,
              loading: false,
              countryList: response?.detail?.data,
            });
          } catch (error) {
            console.log("Api error", error);
          }
        }
      }, 500),
    [token]
  );

  // Update form when user data changes
  React.useEffect(() => {
    if (user) {
      personalForm.reset(user);
    }
  }, [user, personalForm]);

  // Event handlers
  const handleCountry = (query: string) => {
    setCountry({ ...country, loading: true, search: query });
    debouncedHandleCountry(query);
  };

  const handleEditPersonalAndContact = () => {
    setEditingPersonalAndContact(!editingPersonalAndContact);
    if (editingPersonalAndContact) {
      // Reset forms when canceling
      personalForm.reset(user ?? {});
      personalForm.clearErrors();
    }
  };

  const handleEditSecurity = () => {
    if (editingSecurity) {
      passForm.reset();
      passForm.clearErrors();
      setPasswordStatus({ status: null, message: null });
    }
    setEditingSecurity(!editingSecurity);
  };

  const updateUserApi = (data: PersonalFormValues) => {
    if (user) {
      const updatedUser: User = {
        ...user,
        ...data,
      };
      updateUser(updatedUser);
      setEditingPersonalAndContact(false);
    }
  };

  const updatePasswordApi = async (data: PasswordFormValues) => {
    try {
      setPasswordStatus({ status: null, message: null });
      const passwordData: UpdatePassword = {
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
        confirm_password: data.confirmPassword,
      };

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/api/user/change-password`,
        passwordData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setPasswordStatus({
        status: "success",
        message: "Password updated successfully",
      });

      setTimeout(() => {
        setEditingSecurity(false);
        passForm.reset();
      }, 2000);
    } catch (error: any) {
      setPasswordStatus({
        status: "error",
        message: error.response?.data?.message || "Failed to update password",
      });
    }
  };

  const handleSaveEmail = async () => {
    setLoadingEmailOtp(true);
    try {
      const emailValue = personalForm.getValues("email");

      if (!emailRegex.test(emailValue)) {
        personalForm.setError("email", {
          type: "manual",
          message: "Please enter a valid email address",
        });
        setLoadingEmailOtp(false);
        return;
      }

      const response = await axios.put(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/update-contact-details`,
        { email: emailValue },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response?.data?.detail?.status === "success") {
        setShowEmailOtp(true);
      }
    } catch (error: any) {
      console.error("Error updating email:", error);
    }
    setLoadingEmailOtp(false);
  };

  const handleSavePhone = async () => {
    setLoadingPhoneOtp(true);
    try {
      const phoneValue = personalForm.getValues("phone");

      if (!phoneRegex.test(phoneValue)) {
        personalForm.setError("phone", {
          type: "manual",
          message: "Please enter a valid phone number (e.g., +91 12345 67890)",
        });
        setLoadingPhoneOtp(false);
        return;
      }

      const response = await axios.put(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/update-contact-details`,
        { phone: phoneValue },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response?.data?.detail?.status === "success") {
        setShowPhoneOtp(true);
      }
    } catch (error: any) {
      console.error("Error updating phone:", error);
    }
    setLoadingPhoneOtp(false);
  };

  const verifyOtp = async (otp: string[]) => {
    setLoadingEmailOtpVerify(true);
    try {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/verify-email`,
        {
          email: personalForm.getValues("email"),
          otp: otp.join(""),
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (data?.detail?.status === "success") {
        setShowEmailOtp(false);
        setEditingPersonalAndContact(false);
      } else {
        setOtpErrorMessage(data?.detail?.message);
      }
    } catch (error: any) {
      setOtpErrorMessage(error?.response?.data?.detail?.message);
    }
    setLoadingEmailOtpVerify(false);
  };

  const verifyPhoneOtp = async (otp: string[]) => {
    setLoadingPhoneOtpVerify(true);
    try {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/verify-phone`,
        {
          phone: personalForm.getValues("phone"),
          otp: otp.join(""),
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (data?.detail?.status === "success") {
        setShowPhoneOtp(false);
        setEditingPersonalAndContact(false);
      } else {
        setPhoneOtpErrorMessage(data?.detail?.message);
      }
    } catch (error: any) {
      setPhoneOtpErrorMessage(error?.response?.data?.detail?.message);
    }
    setLoadingPhoneOtpVerify(false);
  };

  return (
    <>
      {/* Personal Information & Contact Information Section */}
      <div className="flex justify-between items-center gap-2 md:gap-0">
        <h2 className="text-base sm:text-xl md:text-2xl lg:text-3xl font-semibold text-brand-black">
          Personal Information & Contact Information
        </h2>
        <Button
          icon="edit"
          className={cn(
            "text-sm text-[#F2F3FA] bg-brand px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 rounded-[8px] hover:bg-brand hover:text-brand-white",
            editingPersonalAndContact && "hidden"
          )}
          onClick={handleEditPersonalAndContact}
        >
          Edit Details
        </Button>
      </div>

      <Form {...personalForm}>
        <form onSubmit={personalForm.handleSubmit(updateUserApi)}>
          <div className="">
            {/* Personal Information Fields */}
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              <div className="m-2 col-span-1 sm:col-span-2 lg:col-span-3">
                <FormField
                  control={personalForm.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem className="max-w-32">
                      <Label variant="primary">Title</Label>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={!editingPersonalAndContact}
                        >
                          <SelectTrigger className=" whitespace-nowrap w-full">
                            <SelectValue placeholder="Select Title" />
                          </SelectTrigger>
                          <SelectContent className="shadow-none [&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg- [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                            <SelectItem value="Mr.">Mr.</SelectItem>
                            <SelectItem value="Mrs.">Mrs.</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="m-2">
                <FormField
                  control={personalForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <Label variant="primary">First Name</Label>
                      <FormControl>
                        <Input
                          placeholder="First Name"
                          variant="gradient"
                          disabled={!editingPersonalAndContact}
                          {...field}
                          className="text-neutral-dark placeholder:text-neutral-dark w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="m-2">
                <FormField
                  control={personalForm.control}
                  name="middleName"
                  render={({ field }) => (
                    <FormItem>
                      <Label variant="primary">Middle Name</Label>
                      <FormControl>
                        <Input
                          placeholder="Middle Name"
                          variant="gradient"
                          disabled={!editingPersonalAndContact}
                          {...field}
                          className="text-neutral-dark placeholder:text-neutral-dark w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="m-2">
                <FormField
                  control={personalForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <Label variant="primary">Last Name</Label>
                      <FormControl>
                        <Input
                          placeholder="Last Name"
                          variant="gradient"
                          disabled={!editingPersonalAndContact}
                          {...field}
                          className="text-neutral-dark placeholder:text-neutral-dark w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="m-2">
                <FormField
                  control={personalForm.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <Label variant="primary">Gender</Label>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={!editingPersonalAndContact}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Gender" />
                          </SelectTrigger>
                          <SelectContent className="shadow-none [&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                            <SelectItem value="Male">Male</SelectItem>
                            <SelectItem value="Female">Female</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="m-2">
                <FormField
                  control={personalForm.control}
                  name="dateOfBirth"
                  render={({ field }) => {
                    let selectedDate;
                    try {
                      selectedDate = field.value
                        ? new Date(field.value)
                        : undefined;
                      if (selectedDate && isNaN(selectedDate.getTime())) {
                        selectedDate = undefined;
                      }
                    } catch (error) {
                      selectedDate = undefined;
                    }

                    return (
                      <FormItem>
                        <Label variant="primary">Date of Birth</Label>
                        <Popover
                          open={isCalendarOpen}
                          onOpenChange={setIsCalendarOpen}
                        >
                          <PopoverTrigger asChild>
                            <FormControl>
                              <div className="text-neutral p-px rounded-sm bg-brand-white border-[1px] border-neutral shadow-sm w-full">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  className={cn(
                                    "w-full justify-start px-2 py-5 bg-brand-white hover:bg-brand-white rounded-sm text-left font-normal text-neutral-dark hover:text-neutral-dark ",
                                  )}
                                >
                                  {selectedDate &&
                                    !isNaN(selectedDate.getTime()) ? (
                                    format(selectedDate, "PPP")
                                  ) : (
                                    <span>December 12, 1996</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4" />
                                </Button>
                              </div>
                            </FormControl>
                          </PopoverTrigger>
                          {editingPersonalAndContact && (
                            <PopoverContent
                              className="w-auto p-0 bg-[#F2F3FA]"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={selectedDate}
                                onSelect={(date) => {
                                  if (date) {
                                    field.onChange(date.toISOString());
                                    // Close the calendar popup after selection
                                    setIsCalendarOpen(false);
                                  }
                                }}
                                disabled={(date) =>
                                  date > new Date() ||
                                  date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          )}
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>

              <div className="m-2">
                <FormField
                  control={personalForm.control}
                  name="nationality"
                  render={({ field }) => (
                    <FormItem>
                      <Label variant="primary">Nationality</Label>
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant="ghost"
                            className="w-full text-md text-neutral-dark placeholder:text-neutral-dark hover:text-neutral-dark hover:bg-brand-white justify-between px-4 py-5 bg-brand-white rounded-sm text-left font-normal border border-neutral flex"
                          >
                            {field.value || "Select country"}
                            <ChevronDown className="h-4 w-4 shrink-0 text-neutral-dark stroke-[2.5]" />
                          </Button>
                        </PopoverTrigger>
                        {editingPersonalAndContact && (
                          <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-fuchsia-700">
                            <Command className="bg-brand-white border-neutral rounded-sm shadow-none">
                              <CommandInput
                                placeholder="Search country..."
                                onValueChange={handleCountry}
                              />
                              {country.loading ? (
                                <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Loading...
                                </div>
                              ) : (
                                <CommandList>
                                  {country?.countryList?.length > 0 ? (
                                    <CommandGroup>
                                      {country.countryList.map((item) => (
                                        <CommandItem
                                          key={item.code}
                                          value={item.name}
                                          onSelect={() => {
                                            field.onChange(item.name);
                                            setOpen(false);
                                          }}
                                          className="data-[selected=true]:text-brand-black data-[selected=true]:bg-neutral aria-selected:bg-neutral aria-selected:text-brand-black data-[highlighted=true]:bg-neutral data-[highlighted=true]:text-brand-black hover:bg-neutral hover:text-brand-black"
                                        >
                                          {item.name}
                                        </CommandItem>
                                      ))}
                                    </CommandGroup>
                                  ) : (
                                    <CommandEmpty>
                                      No country found.
                                    </CommandEmpty>
                                  )}
                                </CommandList>
                              )}
                            </Command>
                          </PopoverContent>
                        )}
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Contact Information Fields */}
            <div className="mt-6">
              <h2 className="text-base sm:text-xl md:text-2xl lg:text-3xl font-semibold text-brand-black mb-6">
                Contact Information
              </h2>
              <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                <div className="m-2">
                  <FormField
                    control={personalForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <Label variant="primary">Primary Email</Label>
                        <FormControl>
                          <Input
                            placeholder="Primary Email"
                            variant="gradient"
                            disabled={!editingPersonalAndContact}
                            {...field}
                            className="text-neutral-dark placeholder:text-neutral-dark w-full"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="m-2">
                  <FormField
                    control={personalForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <Label variant="primary">Mobile Number</Label>
                        <FormControl>
                          <Input
                            placeholder="+91 12345 67890"
                            variant="gradient"
                            disabled={!editingPersonalAndContact}
                            {...field}
                            className="text-neutral-dark placeholder:text-neutral-dark w-full"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="m-2">
                  <FormField
                    control={personalForm.control}
                    name="alternatePhone"
                    render={({ field }) => (
                      <FormItem>
                        <Label variant="primary">Alternate Phone</Label>
                        <FormControl>
                          <Input
                            placeholder="+91 12345 67890"
                            variant="gradient"
                            disabled={!editingPersonalAndContact}
                            {...field}
                            className="text-neutral-dark placeholder:text-neutral-dark w-full"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            {editingPersonalAndContact && (
              <div className="flex gap-4 my-8">
                <Button
                  type="button"
                  className="text-md border-brand bg-brand px-6 py-4 rounded-[8px] hover:bg-brand hover:text-brand-white"
                  onClick={handleEditPersonalAndContact}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  icon="save"
                  className="text-md text-white bg-brand px-6 py-4 rounded-[8px] hover:bg-brand hover:text-brand-white"
                >
                  Update
                </Button>
                {/* Email and Phone specific update buttons when needed */}
                {personalForm.formState.dirtyFields.email && (
                  <Button
                    type="button"
                    onClick={handleSaveEmail}
                    disabled={loadingEmailOtp}
                    className="text-md text-white bg-blue-600 px-6 py-4 rounded-[8px] hover:bg-blue-700"
                  >
                    {loadingEmailOtp ? "Sending OTP..." : "Update Email"}
                  </Button>
                )}
                {personalForm.formState.dirtyFields.phone && (
                  <Button
                    type="button"
                    onClick={handleSavePhone}
                    disabled={loadingPhoneOtp}
                    className="text-md text-white bg-green-600 px-6 py-4 rounded-[8px] hover:bg-green-700"
                  >
                    {loadingPhoneOtp ? "Sending OTP..." : "Update Phone"}
                  </Button>
                )}
              </div>
            )}
          </div>
        </form>
      </Form>

      {updatedStatus === "success" && (
        <div className="mb-6 flex justify-center">
          <span className="text-green-600">Changes Updated Successfully</span>
        </div>
      )}

      <div className="flex w-full h-[0.5px] bg-[#B4BBE8] mt-6"></div>

      {/* Security and Safety Information Section */}
      <div className="mt-6">
        <div className="flex justify-between items-center gap-2 md:gap-4 mb-3">
          <h2 className="text-base sm:text-xl md:text-2xl lg:text-3xl font-semibold text-brand-black">
            Security and Safety Information
          </h2>
          <Button
            type="button"
            icon="edit"
            className={cn(
              "text-sm md:text-md text-white bg-brand hover:bg-brand hover:text-brand-white px-3 md:px-6 py-2 md:py-4 rounded-[8px] whitespace-nowrap",
              editingSecurity && "hidden"
            )}
            onClick={handleEditSecurity}
          >
            Change password
          </Button>
        </div>

        <Form {...passForm}>
          <form onSubmit={passForm.handleSubmit(updatePasswordApi)}>
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              <div className="m-2 col-span-1 sm:col-span-1 lg:col-span-1">
                <FormField
                  control={passForm.control}
                  name="oldPassword"
                  render={({ field }) => (
                    <FormItem>
                      <Label variant="primary">Old Password</Label>
                      <FormControl>
                        <Input
                          placeholder="*******"
                          type="password"
                          variant="gradient"
                          disabled={!editingSecurity}
                          {...field}
                          className="text-neutral-dark placeholder:text-neutral-dark w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="hidden sm:block m-2"></div>
              <div className="hidden lg:block m-2"></div>

              {editingSecurity && (
                <>
                  <div className="m-2 col-span-1">
                    <FormField
                      control={passForm.control}
                      name="newPassword"
                      render={({ field }) => (
                        <FormItem>
                          <Label variant="primary">New Password</Label>
                          <FormControl>
                            <Input
                              placeholder="*******"
                              type="password"
                              variant="gradient"
                              {...field}
                              className="text-neutral-dark placeholder:text-neutral-dark w-full"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="m-2 col-span-1">
                    <FormField
                      control={passForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <Label variant="primary">Confirm Password</Label>
                          <FormControl>
                            <Input
                              placeholder="*******"
                              type="password"
                              variant="gradient"
                              {...field}
                              className="text-neutral-dark placeholder:text-neutral-dark w-full"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="hidden lg:block m-2"></div>
                </>
              )}
            </div>

            {passwordStatus.status && (
              <div
                className={`mt-4 ${passwordStatus.status === "success" ? "text-green-600" : "text-red-600"}`}
              >
                {passwordStatus.message}
              </div>
            )}

            {editingSecurity && (
              <div className="flex gap-4 my-8">
                <Button
                  type="button"
                  className="text-md border-brand hover:bg-brand text-brand-white bg-brand hover:text-brand-white px-6 py-4 rounded-[8px]"
                  onClick={handleEditSecurity}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  icon="save"
                  className="text-md text-white bg-brand px-6 py-4 rounded-[8px] hover:bg-brand hover:text-brand-white"
                >
                  Save password
                </Button>
              </div>
            )}
          </form>
        </Form>
      </div>

      <div className="text-md font-semibold text-[#1E1E76] mt-10 mb-8">
        Privacy Policy | Terms of Service
      </div>

      {/* OTP Verification Modals */}
      {showEmailOtp && (
        <OtpVerificationScreen
          onVerify={verifyOtp}
          handleResend={handleSaveEmail}
          loadingEmailOtp={loadingEmailOtp}
          loadingEmailOtpVerify={loadingEmailOtpVerify}
          otpErrorMessage={otpErrorMessage}
          close={() => setShowEmailOtp(false)}
        />
      )}

      {showPhoneOtp && (
        <OtpVerificationScreen
          onVerify={verifyPhoneOtp}
          handleResend={handleSavePhone}
          loadingEmailOtp={loadingPhoneOtp}
          loadingEmailOtpVerify={loadingPhoneOtpVerify}
          otpErrorMessage={phoneOtpErrorMessage}
          close={() => setShowPhoneOtp(false)}
          type="phone"
        />
      )}
    </>
  );
};

export default ProfileDetails;
